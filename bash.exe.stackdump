Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x1FE8E
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210286019, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B80  000210068E24 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E60  00021006A225 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAE18E0000 ntdll.dll
7FFAE0640000 KERNEL32.DLL
7FFADEE30000 KERNELBASE.dll
7FFADFDC0000 USER32.dll
000210040000 msys-2.0.dll
7FFADF420000 win32u.dll
7FFAE0800000 GDI32.dll
7FFADF2E0000 gdi32full.dll
7FFADF5D0000 msvcp_win.dll
7FFADECE0000 ucrtbase.dll
7FFADFD00000 advapi32.dll
7FFAE13E0000 msvcrt.dll
7FFAE0090000 sechost.dll
7FFAE1600000 RPCRT4.dll
7FFADE150000 CRYPTBASE.DLL
7FFADEC40000 bcryptPrimitives.dll
7FFAE11E0000 IMM32.DLL
